# SmartSecHub Resource Extraction Instructions for ChatGPT

## Overview

You are tasked with analyzing cybersecurity-related URLs and extracting structured information to create JSON resources for SmartSecHub. Follow these instructions carefully to generate properly formatted JSON data.

## Your Task

1. Visit each provided URL
2. Read and analyze the content
3. Extract relevant information
4. Generate a JSON array of resources following the specified schema

## JSON Schema Requirements

Each resource object must contain the following fields:

### Required Fields:

- **title** (string): The main title of the content
- **url** (string): The original URL provided
- **createdAt** (string): The date the content was created in `YYYY-MM-DD` format
- **tags** (array): THIS is really important field, analyze the content and add tags that describe the content. Use existing tags - I will provide them to you.
- **type** (string): One of the following values:
  - `"article"` - Blog posts, articles, written content
  - `"video"` - YouTube videos, video tutorials
  - `"tweet"` - Twitter/X posts
  - `"github"` - GitHub repositories, code projects
  - `"papers"` - Research papers, whitepapers, academic content
  - `"sites"` - Websites, tools, platforms

### Optional Fields:
- **customTwitterId** (string): If the author's Twitter ID is not in the database, add it here

### Fields to exclude:
- Do not include `id`, `likesCount`, `dislikesCount`,

## Content Analysis Guidelines

### Title Extraction:

- Use the actual title from the webpage/content
- If no clear title exists
- Keep titles concise but informative
- Remove unnecessary prefixes like "Blog:" or suffixes like "| Company Name"

### Type Classification:

- **article**: Blog posts, tutorials, guides, news articles, documentation
- **video**: Any video content (YouTube, Vimeo, etc.)
- **tweet**: Twitter/X posts and threads
- **github**: GitHub repositories, code samples, open source projects
- **papers**: Academic papers, research documents, whitepapers, technical reports
- **sites**: Tools, platforms, websites, online services, dashboards


## Output Format

Return a valid JSON array with this exact structure:

```json
[
  {
    "title": "Example Security Article Title",
    "url": "https://example.com/article",
    "type": "article",
    "createdAt": <current date>,
    "customTwitterId": <i can provide this>
    "authorId": <I will provide this>
    "tags": []
  },
  {
    "title": "Another Resource Title",
    "url": "https://example.com/video",
    "type": "video",
    "createdAt": <current date>,
    "customTwitterId": <i can provide this>
    "authorId": <I will provide this>
    "tags": []
  }
]
```

## Quality Standards

### Content Relevance:

Only include content related to:

- Cybersecurity
- Information security
- Penetration testing
- Vulnerability research
- Security tools and techniques
- Incident response
- Security architecture
- Compliance and governance
- Privacy and data protection

### Accuracy Requirements:

- Verify the URL is accessible
- Ensure the title matches the actual content
- Confirm the type classification is correct

## Error Handling

If a URL is inaccessible or contains irrelevant content:

- Skip that URL
- Do not include it in the JSON output
- Mention skipped URLs in a separate note after the JSON

## Example Input/Output

**Input URLs:**

- https://owasp.org/www-project-top-ten/
- https://github.com/danielmiessler/SecLists

**Expected Output:**

```json
[
  {
    "title": "OWASP Top 10 Web Application Security Risks",
    "url": "https://owasp.org/www-project-top-ten/",
    "type": "sites",
    "tags": []
  },
  {
    "title": "SecLists - Security Testing Lists",
    "url": "https://github.com/danielmiessler/SecLists",
    "type": "github",
    "tags": []
  }
]
```

## Final Notes

- Always return valid JSON that can be parsed
- Double-check URLs for typos
- Ensure all required fields are present
- Focus on cybersecurity relevance
- Maintain consistent formatting

---

**Instructions for use:** Paste the URLs you want to analyze after this prompt, and I will generate the JSON array following these specifications.

---

Input data:
Tags: [{"id":"0KTIQ8XLQNZ7NjLkAYXG","group":"CommonAttacks","name":"Replay Attack"},{"id":"0gjSQN9dAJQ22SJ9AbUP","name":"Portfolio","group":"CareerPath"},{"id":"1ZpOUrVCRnC2tctR2Dos","group":"HotTopics","name":"For Beginners"},{"id":"1p4d4NcqrDESeZ2Zj2mK","name":"Monitoring","group":"DeveloperInfrastructure"},{"id":"1qkCyKhzb4R2PvywFay3","name":"ERC-20","group":"ERC"},{"id":"2LYr7ETBRGMBsZylFLOK","group":"CareerPath","name":"Interview"},{"id":"2oaKAn3yRia9KwivRRXS","name":"DAO attacks","group":"SpecificAttackVectors"},{"id":"37j81peolsCPS7NFZPxH","group":"EIP","name":"EIP-1153"},{"id":"3BcULpb81i5PnSfkwfEv","name":"Reentrancy","group":"CommonAttacks"},{"id":"3L7TImBV3lL8emQekWsR","group":"DeFiAttacks","name":"DeFi Attacks thread"},{"id":"45KNwXbZn7DeL0FrT586","name":"EIP-4844","group":"EIP"},{"id":"4FgCNWJs5zNv6PJBLSJg","name":"EIP-1559","group":"EIP"},{"id":"4VOo7uOJSF1FJTZJQTCR","name":"Logic Attack","group":"SpecificAttackVectors"},{"id":"4huJ77CchTXKePBEL5oQ","name":"Hack Analysis","group":"HotTopics"},{"id":"4ucGDXToB9JSirKnnDLX","name":"Rounding error","group":"DeFiAttacks"},{"id":"4zf2u169OeWVxIjQFj6r","name":"Platforms","group":"CareerPath"},{"id":"56mmYOhZuGLoMRclYp7K","name":"Audit Summary","group":"CareerPath"},{"id":"5iuzJs977rpmNTjFafIN","group":"ERC","name":"ERC-2771"},{"id":"5o1eNwwICnXL7AAqv5AS","name":"Formal Verification","group":"Tests"},{"id":"5t38yISE8SUmYg3dGFj5","name":"Multichain","group":"CrossChain"},{"id":"6QOO5ikGfG8CvTtepJYn","group":"DeFiAttacks","name":"Rewards issue"},{"id":"6Z5kaWZ5huVEYcSU4JXD","name":"Common Attacks Thread","group":"CommonAttacks"},{"id":"6vW6wt9AusD4nZqXJmh4","group":"ERC","name":"Weird tokens"},{"id":"8v7DQgO6KBbg63qbGB53","name":"AI","group":"DeveloperInfrastructure"},{"id":"9rKQBuFwWF1TOjbAppju","group":"CommonAttacks","name":"Low Level Call Attacks"},{"id":"A12koI5ST471PSxr3422","group":"SpecificAttackVectors","name":"Input validation "},{"id":"AC1rQoPKjRtafrGYVJUD","group":"Tests","name":"Fuzzing"},{"id":"Aes8KjP90PuogF6jHjse","group":"Challenges","name":"Exercises"},{"id":"AlSZ9iLa4N08U6dWdMk6","name":"EIP-2535","group":"EIP"},{"id":"BCy99kIsPcff6W3wSUe0","name":"Bridge","group":"CrossChain"},{"id":"BFf2XPPKgJKC8NlLqqRv","group":"HotTopics","name":"Bug analysis"},{"id":"C5QWI6VOUCkjXxwzN2Qa","name":"Oracles","group":"BlockchainConceptsAndTechniques"},{"id":"CWQqmZ7rD27ypYj8UcGQ","name":"Foundry","group":"Tests"},{"id":"CoRMGPGivvzljGAvjKPL","name":"Cosmos Blockchain","group":"CrossChain"},{"id":"DKr1xdh3rIt0JvDF5QFP","group":"ERC","name":"ERC-7265"},{"id":"DROUrE58Ito1Z3D3vAF5","name":"Sandwitch Attack","group":"CommonAttacks"},{"id":"EnXEEcU4FJ8Q98fEVm83","name":"Checklists","group":"HotTopics"},{"id":"G3lzaQE8mSv8lZkX2nHq","name":"EIP-1167","group":"EIP"},{"id":"GTySlknepq7Hz3WhBGKb","group":"BlockchainConceptsAndTechniques","name":"Blockchain thread"},{"id":"Heblu6xlwam2xuZbyReD","group":"CrossChain","name":"Sui"},{"id":"HoFT8In21p0D3jQKZrhn","name":"ERC-1155","group":"ERC"},{"id":"HttYPmBNeYVpBnpOl637","group":"ERC","name":"ERC-4337"},{"id":"IpWcWz6WXNvSdZMZGgy3","group":"DeFi","name":"Uniswap"},{"id":"JtsYKSwwNeqpbcVRQOP7","group":"BlockchainConceptsAndTechniques","name":"MultiSig"},{"id":"Ka4hrWpckuYMpvEhNqi1","name":"Pools","group":"DeFi"},{"id":"L3k7kz6w56vWC0UI1ocf","name":"Libraries","group":"DeveloperInfrastructure"},{"id":"LE7I0nRNLElo3WfkWCJU","name":"ERC-7512","group":"ERC"},{"id":"LHVf7Z1MfyuG44j7nGdu","name":"Developer Tools","group":"DeveloperInfrastructure"},{"id":"MA6IJ3guocASszmK3xBG","group":"CommonAttacks","name":"Gas Griefing"},{"id":"MLfLntYg0AzCJP0eusHn","group":"Challenges","name":"CTF"},{"id":"MTcpTVk1QgX0cER1W1cy","name":"Phishing Attack","group":"CommonAttacks"},{"id":"N6nnaurtRIJJ1D8SRQUW","group":"Rest","name":"Math / Algo"},{"id":"N93ctNBeh4xdnAe1sZgE","group":"DeFiAttacks","name":"Collateral issue"},{"id":"NMkaqjuRvBUoTPh2IT9K","group":"DeveloperInfrastructure","name":"Transaction Explorer"},{"id":"Ob451v7FYrIN6VwsCiuV","group":"CommonAttacks","name":"DoS attack"},{"id":"PNr8PdU8KeAIpJpi9B7h","name":"Solana","group":"CrossChain"},{"id":"PYUvBBfOhliPpWpRWyls","name":"Project tutorials","group":"BlockchainConceptsAndTechniques"},{"id":"Pb6lAavov4EvY35K8NBL","group":"DeFi","name":"LSD"},{"id":"Phas2C09dCtI5dHOCLGa","name":"Math Attack","group":"SpecificAttackVectors"},{"id":"PjX7eaJDM08wb31seW0c","group":"DeFi","name":"DeFi Essentianls"},{"id":"PpSr1fb7uFNRF3xESZ20","group":"BlockchainConceptsAndTechniques","name":"Gas Optimization"},{"id":"PynSpmSm4xAipCHRoH7x","group":"DeFi","name":"DeFi Advanced"},{"id":"R2IyrwFgmPZPYOUhDPfc","group":"BlockchainConceptsAndTechniques","name":"Signatures"},{"id":"RM6W06ceEwVgmYLBtxa1","name":"Auditor Mindset","group":"CareerPath"},{"id":"RlRoI1MAKrPEEMSqu1IJ","name":"Options","group":"DeFi"},{"id":"SUcGbtvKytAKvi91CSE6","name":"ERC-3156","group":"ERC"},{"id":"UP83aSCDVmkCEsm9pgCl","name":"Zero Knowledge","group":"Rest"},{"id":"Ur0bP7CmQmTVu8h83HqV","name":"Overflow/Underflow Attack","group":"CommonAttacks"},{"id":"V3ahbBRnjwbjnriZkekS","group":"Rest","name":"Longread"},{"id":"V9B2u743lxnEtwSLglsE","group":"CommonAttacks","name":"Proxy Attack"},{"id":"VbNSPnBXbZdFUkVgCfI5","name":"Code analysis tool/lib","group":"DeveloperInfrastructure"},{"id":"WaQfOgfchOEYIq6UGkUT","group":"CommonAttacks","name":"Selfdestruct attack"},{"id":"X1rQY0nlQSQ4T71SMvJ5","name":"ERC-1363","group":"ERC"},{"id":"X2PsYaGcpjovEyLdQroC","group":"Rest","name":"ALL-IN-ONE"},{"id":"Y2UCqaI2W4hlsAtnec2E","name":"Ethernaut","group":"Challenges"},{"id":"Z0y9e7DpOr6zUT5Jhd9T","name":"TWAP","group":"DeFi"},{"id":"Z9olDozhwzuk9cNuQcMb","name":"Spot the Bug🐛","group":"HotTopics"},{"id":"ZLWPaY76TpMKdLeRDLmy","name":"ERC thread","group":"ERC"},{"id":"a750Jx0G311BHn9NAUm1","group":"EIP","name":"EIP-6963"},{"id":"aEThYHPpOQGMC69OLTBe","name":"Signature Database","group":"DeveloperInfrastructure"},{"id":"aVQywMtpDAah4e3Zwut6","group":"ERC","name":"ERC-777"},{"id":"aoFmtIlUT6ORD5npksMz","group":"DeFi","name":"Perpetuals"},{"id":"b3u0cV1FYVAc8PAiy1Ow","name":"⭐ SmartSecHub choice ⭐","group":"HotTopics"},{"id":"bHNj3a0VOsEfxAruyNs4","group":"BlockchainConceptsAndTechniques","name":"Code Templates"},{"id":"cWFrvDg3udtF4chqlRh7","group":"Tests","name":"POCs"},{"id":"dgFHXShRyXzZbT0ydyhk","group":"EIP","name":"EIP-3525"},{"id":"dvnswmE6aEvtFyONIHJQ","group":"CareerPath","name":"Contests/Bounties"},{"id":"eOW5aXSXLezQwBTa3Sbf","group":"DeFi","name":"CDP"},{"id":"eqjQzxbiZtU1rZrg5DsJ","name":"Bots","group":"Rest"},{"id":"fPAmOI5Bcgz4rtIs1cLh","name":"ERC-4626","group":"ERC"},{"id":"gNxWb2O2pCs4ESP3I1qY","name":"Flash Loan Attack","group":"DeFiAttacks"},{"id":"giCoYBP7mJ0LTSQpIaZ2","name":"Staking issues","group":"DeFiAttacks"},{"id":"giORPpbdsxXjVcNIdBnt","group":"DeFiAttacks","name":"Miscalculation issue"},{"id":"hGfCFQe7M9kq6MHgd47L","group":"DeFi","name":"AMM"},{"id":"hXA8feQxEBOaHA664C8G","name":"ERC-6900","group":"ERC"},{"id":"i6dAELk3SwqcaHeSEAtO","group":"BlockchainConceptsAndTechniques","name":"Proxies / Upgradable"},{"id":"iOWX23T1YTYFHJB5Fge2","name":"ERC-721","group":"ERC"},{"id":"iYf6A0I3f4jjQvuEDjC6","name":"Courses","group":"CareerPath"},{"id":"iwYxjmnmWAX7IxhHdyEq","name":"Ethereum / EVM codes","group":"BlockchainConceptsAndTechniques"},{"id":"j0xq1D6qMWVzEZgODTu0","group":"Challenges","name":"DamnVulnerableDeFi"},{"id":"jQsnNnWZXsMRvhSuj6sO","name":"Lending/Borrowing Attack","group":"DeFiAttacks"},{"id":"jSqSiqdmmjRV6F0moQ4b","name":"Threads","group":"HotTopics"},{"id":"kZts0zkNI6SzrEX35vJM","name":"Testing","group":"Tests"},{"id":"lMidBLH8oW76XK959oAj","group":"CommonAttacks","name":"Front Running attack"},{"id":"lrFOZ5lwV7cwiDV4hLTz","group":"DeFiAttacks","name":"Oracle Attacks"},{"id":"lvIvBCizzzf5cSNVRkVV","group":"DeFi","name":"Compound"},{"id":"nGT53tEvwoW0FbUrQ9cJ","name":"Solidity Essentials","group":"BlockchainConceptsAndTechniques"},{"id":"os7d1p4bToBA67WUS1br","name":"EIP-3074","group":"EIP"},{"id":"peIkxR4s0AUl50Mrtohh","group":"DeveloperInfrastructure","name":"Smart Contract reader/decoder"},{"id":"pyVp3LyGcsPuxu1EjOFe","name":"Randomness issue","group":"CommonAttacks"},{"id":"qIS7LSfRljRTH2xIXQOW","group":"BlockchainConceptsAndTechniques","name":"DAO / Governance"},{"id":"qpzyvhlsjIUafrBUxRi4","name":"NFT Attacks","group":"SpecificAttackVectors"},{"id":"r3TtkloePzTRW7QcMV0m","name":"ParadigmCTF","group":"Challenges"},{"id":"rT97p6Ga7IeLMJu5GgdX","name":"Layer 2","group":"BlockchainConceptsAndTechniques"},{"id":"rUUpZOLiiR6z78CJ3EdB","name":"DeFi thread","group":"DeFi"},{"id":"soMp3rG2bZnvWEUkRU7x","name":"Precision loss issue","group":"DeFiAttacks"},{"id":"tvaECi9G7ZHlNQOMJ4hb","group":"Rest","name":"Digest"},{"id":"tz1KDc2Q72FX9uq6qvMG","name":"Invariant Testing","group":"Tests"},{"id":"u64Tjgz1sAMitUE1aa7s","name":"Rust","group":"CrossChain"},{"id":"uI1sw2kJPoxTODU5LYIc","name":"Roadmaps","group":"CareerPath"},{"id":"uMOJxy0vN21Xbn5gt8VE","name":"Access Control issue","group":"CommonAttacks"},{"id":"uVZSKCOuMIMl0G7uYBwi","group":"BlockchainConceptsAndTechniques","name":"Defence/Design patterns"},{"id":"vH9apDSFhhaIdCKmWbBc","name":"Quiz","group":"Challenges"},{"id":"vsBw83D0KlshSS43O3mM","group":"EIP","name":"EIP-3448"},{"id":"wA5OodzeKZX2EyZzhdTW","name":"Live Audit","group":"CareerPath"},{"id":"wEEBKN1AyhJDRwQp05iF","name":"ERC-404","group":"ERC"},{"id":"wUOhOYBMohFRb0iPSiAq","name":"ERC-6551","group":"ERC"},{"id":"xy06rCsyLD9SUt0M6FQd","name":"Misc tools","group":"DeveloperInfrastructure"},{"id":"yK2hVFFQ9QM1alchG81u","group":"EIP","name":"EIP-2930"},{"id":"ymn1ZeB3zXJHIlhkAegR","name":"Slippage Attack","group":"DeFiAttacks"},{"id":"yzkgFzIyyz4FarC3LKTt","name":"Audit Report","group":"HotTopics"},{"id":"z1keIgxY6PqdGpyDascv","name":"Assembly/Yul","group":"BlockchainConceptsAndTechniques"},{"id":"z5FubOqsgDfodm0RnVBn","name":"Price Manipulation Attack","group":"DeFiAttacks"},{"id":"zYOoubPQ7OaV5l8BfPY3","group":"CrossChain","name":"Cross-Chain thread"},{"id":"zzgs9zWRYClGeQ8BHbBc","group":"CrossChain","name":"LayerZero"}]

author id - CHEb8JyOp6apO8sawyB7
Twitter ID - @HalbornSecurity

Links:

https://www.youtube.com/watch?v=48xY-f60pa0
https://www.halborn.com/blog/post/explained-the-gmx-hack-july-2025
https://www.youtube.com/watch?v=Z-BrFTc80No
https://www.halborn.com/blog/post/securing-the-blockchain-against-quantum-computing
https://www.halborn.com/blog/post/top-risks-of-investing-in-internet-capital-markets
https://www.halborn.com/blog/post/explained-the-resupply-hack-june-2025
https://www.halborn.com/blog/post/explained-the-nobitex-hack-june-2025
https://www.halborn.com/blog/post/record-16-billion-password-leak-what-the-breach-means-for-web3
https://www.halborn.com/blog/post/explained-the-betterbank-hack-august-2025
https://www.youtube.com/watch?v=hL7bWrXCKkA
https://www.halborn.com/blog/post/why-proof-of-reserves-is-critical-for-stablecoin-security
https://www.halborn.com/blog/post/explained-the-btcturk-hack-august-2025
https://www.halborn.com/blog/post/explained-the-odin-fun-hack-august-2025
https://www.halborn.com/blog/post/explained-the-monero-51-percent-attack-august-2025
https://www.youtube.com/watch?v=3pqbU2f5F7o
https://www.halborn.com/blog/post/security-best-practices-for-digital-asset-treasury-dat-companies
https://www.halborn.com/blog/post/designing-for-failure-security-architecture-that-assumes-breach
https://www.halborn.com/blog/post/what-is-a-digital-asset-treasury-dat-company
https://www.halborn.com/blog/post/month-in-review-top-defi-hacks-of-july-2025
https://www.halborn.com/blog/post/explained-the-credix-hack-august-2025
