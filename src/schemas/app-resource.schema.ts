import { z } from 'zod';
import { AppResourceType } from '../core.types';

export const appResourceSchema = z.object({
  id: z.string().optional(),
  title: z.string().min(1, 'Title is required'),
  url: z.string().url({ message: 'Invalid URL' }),
  description: z.string().optional(),
  imageUrl: z
    .union([z.string().url({ message: 'Invalid URL' }), z.literal('')])
    .optional(),
  type: z.nativeEnum(AppResourceType, {
    message: 'Invalid resource type',
  }),
  tags: z.array(z.string()).default([]),
  authorId: z.string().optional(),
  customTwitterId: z.string().optional(),
  createdAt: z.string().optional(),
  likesCount: z.number().default(0),
  dislikesCount: z.number().default(0),
  assessmentId: z.string().optional(),
});

export const bulkResourceSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  url: z.string().url({ message: 'Invalid URL' }),
  description: z.string().optional(),
  type: z.nativeEnum(AppResourceType, {
    message: 'Invalid resource type',
  }),
  tags: z.array(z.string()).default([]),
});

export const bulkResourceArraySchema = z.array(bulkResourceSchema);

export type AppResourceFormData = z.infer<typeof appResourceSchema>;
export type BulkResourceData = z.infer<typeof bulkResourceSchema>;
export type BulkResourceArrayData = z.infer<typeof bulkResourceArraySchema>;
