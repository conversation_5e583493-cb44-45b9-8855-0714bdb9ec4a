import { useState } from 'react';
import { AppTag } from '../../core.types';
import { ManageTagForm } from './ManageTagForm';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

type EditTagModalProps = {
  onSubmit: (values: AppTag) => void;
  defaultValues: AppTag;
  children: React.ReactNode;
};

export const EditTagModal = ({
  defaultValues,
  children,
  onSubmit,
}: EditTagModalProps) => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className='sm:max-w-[425px] max-h-[90vh] overflow-auto'
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle>Edit tag</DialogTitle>
        </DialogHeader>
        <ManageTagForm
          onSubmit={(tag: AppTag) => {
            onSubmit(tag);
            setOpen(false);
          }}
          defaultValues={defaultValues}
        />
      </DialogContent>
    </Dialog>
  );
};
