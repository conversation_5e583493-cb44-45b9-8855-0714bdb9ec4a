import { useCallback } from 'react';
import { toast } from 'react-toastify';
import { createAuthor } from '../../api';
import { useRootContext } from '../../context/useRootContext';
import { useAdminCounters } from '../../context/AdminCountersContext';
import { AppAuthor } from '../../core.types';
import { ManageAuthorForm } from './ManageAuthorForm';
import { ManageAuthorItem } from './ManageAuthorItem';

export const ManageAuthors = () => {
  const { setAuthors, authors } = useRootContext();
  const { refreshCounters } = useAdminCounters();

  const onAuthorCreate = useCallback(
    async (values: AppAuthor) => {
      const isAlreadyAdded = authors.find(
        (author) => author.name === values.name,
      );

      if (isAlreadyAdded) {
        toast.error('Author is already added');
        return;
      }

      const tagId = await createAuthor(values as any);
      if (tagId) {
        setAuthors((prevAuthors: <AUTHORS>
          ...prevAuthors,
          { ...values, id: tagId } as AppAuthor,
        ]);
        // Refresh counters after successful creation
        refreshCounters();
      }
    },
    [authors, setAuthors, refreshCounters],
  );

  return (
    <div className='flex gap-3 m-4'>
      <div className='w-[30%]'>
        <h3>Add Author</h3>
        <ManageAuthorForm onSubmit={onAuthorCreate} />
      </div>
      <div className='flex flex-wrap gap-2 w-[70%]'>
        {authors.map((author) => (
          <ManageAuthorItem key={author.id} author={author} />
        ))}
      </div>
    </div>
  );
};
