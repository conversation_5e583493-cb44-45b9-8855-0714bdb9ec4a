import { AppTag, UpdateType } from '../core.types';

import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  query,
  setDoc,
} from 'firebase/firestore';
import { toast } from 'react-toastify';
import { firestore } from '../services/firebase-service';
import { changeUpdateRecord } from './update.api';

export async function createTag(tagData: Omit<AppTag, 'id'>): Promise<string> {
  try {
    const tagRef = collection(firestore, 'tag');
    const docRef = doc(tagRef);
    await setDoc(docRef, {
      ...tagData,
    });
    await changeUpdateRecord(UpdateType.Tag);
    toast.success('Tag created!');
    return docRef.id;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error creating event:', error);
    return '';
  }
}

export async function getTags(): Promise<AppTag[]> {
  try {
    const q = query(collection(firestore, 'tag'));

    const tagSnapshot = await getDocs(q);
    const tags: AppTag[] = [];
    tagSnapshot.forEach((doc) => {
      tags.push({ id: doc.id, ...doc.data() } as AppTag);
    });
    return tags;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving tags list:', error);
    return [];
  }
}

export async function updateTag(tagData: AppTag): Promise<void> {
  try {
    const tagRef = doc(collection(firestore, 'tag'), tagData.id);
    await setDoc(
      tagRef,
      {
        ...tagData,
      },
      { merge: true },
    );
    await changeUpdateRecord(UpdateType.Tag);
    toast.success('Tag updated!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error updating tag:', error);
  }
}

export const deleteTag = async (tagId: string) => {
  try {
    // Delete event document in Firestore
    const eventRef = doc(collection(firestore, 'tag'), tagId);
    await deleteDoc(eventRef);
    await changeUpdateRecord(UpdateType.Tag);
    toast.success('Tag deleted!');
    // Delete corresponding file in storage
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error deleting tag:', error);
  }
};
