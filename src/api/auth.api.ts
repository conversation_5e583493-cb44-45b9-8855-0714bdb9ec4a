import {
  signInWithPopup,
  TwitterAuthProvider,
  GoogleAuthProvider,
} from 'firebase/auth';

import { collection, getDocs, query, where } from 'firebase/firestore';
import { toast } from 'react-toastify';
import { firebaseAuth, firestore } from '../services/firebase-service';

export const signInWithTwitter = async () => {
  const provider = new TwitterAuthProvider();

  try {
    await signInWithPopup(firebaseAuth, provider);
    toast.success('Signed in!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error during sign in:', error);
  }
};

export const signInWithGoogle = async () => {
  const provider = new GoogleAuthProvider();

  try {
    await signInWithPopup(firebaseAuth, provider);
    toast.success('Signed in!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error during sign in:', error);
  }
};

export const signOut = async () => {
  try {
    await firebaseAuth.signOut();
    toast.success('Signed out!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error during sign out:', error);
  }
};

export const getCurrentUserId = () => firebaseAuth.currentUser?.uid;

export const checkIfUserIsAdmin = async () => {
  try {
    const q = query(
      collection(firestore, 'admin'),
      where('id', '==', getCurrentUserId()),
    );

    const adminSnapshot = await getDocs(q);
    const admins: any[] = [];
    adminSnapshot.forEach((doc) => {
      admins.push({ id: doc.id, ...doc.data() });
    });
    return admins.length > 0;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error checking if current User is admin:', error);
    throw error;
  }
};
