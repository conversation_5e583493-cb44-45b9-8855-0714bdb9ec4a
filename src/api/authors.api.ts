import { AppAuthor, UpdateType } from '../core.types';

import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  query,
  setDoc,
} from 'firebase/firestore';
import { toast } from 'react-toastify';
import { firestore } from '../services/firebase-service';
import { changeUpdateRecord } from './update.api';

export const AUTHOR_COLLECTION = 'blogger';

export const createAuthor = async (authorData: AppAuthor) => {
  try {
    const authorRef = collection(firestore, AUTHOR_COLLECTION);
    const docRef = doc(authorRef);
    await setDoc(docRef, {
      ...authorData,
    });
    await changeUpdateRecord(UpdateType.Author);
    toast.success('Author created!');
    return docRef.id;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error creating event:', error);
  }
};

export const updateAuthor = async (authorData: AppAuthor) => {
  try {
    const authorRef = doc(
      collection(firestore, AUTHOR_COLLECTION),
      authorData.id,
    );
    await setDoc(
      authorRef,
      {
        ...authorData,
      },
      { merge: true },
    );
    await changeUpdateRecord(UpdateType.Author);
    toast.success('Author updated!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error updating author:', error);
  }
};

export const deleteAuthor = async (authorId: string) => {
  try {
    // Delete event document in Firestore
    const eventRef = doc(collection(firestore, AUTHOR_COLLECTION), authorId);
    await deleteDoc(eventRef);
    await changeUpdateRecord(UpdateType.Author);
    toast.success('Author deleted!');
    // Delete corresponding file in storage
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error deleting author:', error);
  }
};

export const getAuthors = async (): Promise<AppAuthor[]> => {
  try {
    const q = query(collection(firestore, AUTHOR_COLLECTION));

    const authorSnapshot = await getDocs(q);
    const authors: AppAuthor[] = [];
    authorSnapshot.forEach((doc) => {
      authors.push({ id: doc.id, ...doc.data() } as AppAuthor);
    });
    return authors;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving authors list:', error);
    return [];
  }
};
