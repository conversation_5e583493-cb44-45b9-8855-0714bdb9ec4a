import { AppReadMark } from '../core.types';

import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  query,
  setDoc,
  where,
} from 'firebase/firestore';
import { toast } from 'react-toastify';
import { firestore } from '../services/firebase-service';
import { getCurrentUserId } from './auth.api';

const READMARK_COLLECTION = 'readmark';

export const getAllUserReadMarks = async (): Promise<AppReadMark[]> => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return [];
    }

    const readmarkRef = collection(firestore, READMARK_COLLECTION);

    const q = query(readmarkRef, where('userId', '==', userId));

    const readmarkSnapshot = await getDocs(q);
    const readmarks: AppReadMark[] = [];
    readmarkSnapshot.forEach((doc) => {
      readmarks.push({ id: doc.id, ...doc.data() } as AppReadMark);
    });

    return readmarks;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error retrieving app readmarks:', error);
    return [];
  }
};

export const addReadMark = async (resourceId: string) => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return;
    }

    const q = query(
      collection(firestore, READMARK_COLLECTION),
      where('userId', '==', userId),
      where('resourceId', '==', resourceId),
    );
    const readmarkSnapshot = await getDocs(q);
    const readmarkList: AppReadMark[] = [];
    readmarkSnapshot.forEach((doc) => {
      readmarkList.push({ id: doc.id, ...doc.data() } as AppReadMark);
    });
    if (readmarkList.length > 0) {
      toast.error('You already bookmarked this resource!');
      return;
    }

    const readmarkRef = collection(firestore, READMARK_COLLECTION);
    const docRef = doc(readmarkRef);
    await setDoc(docRef, {
      userId,
      resourceId,
    });
    toast.success('Readmark added!');
    return docRef.id;
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error marking readMark:', error);
  }
};

export const removeReadMark = async (resourceId: string) => {
  try {
    const userId = await getCurrentUserId();
    if (!userId) {
      return;
    }

    const q = query(
      collection(firestore, READMARK_COLLECTION),
      where('userId', '==', userId),
      where('resourceId', '==', resourceId),
    );
    const readMarkSnapshot = await getDocs(q);
    const readmarksList: AppReadMark[] = [];
    readMarkSnapshot.forEach((doc) => {
      readmarksList.push({ id: doc.id, ...doc.data() } as AppReadMark);
    });

    const bookmarkRef = doc(
      collection(firestore, READMARK_COLLECTION),
      readmarksList[0].id,
    );
    await deleteDoc(bookmarkRef);
    toast.success('Readmark removed!');
  } catch (error) {
    toast.error((error as Error).message);
    console.error('Error unchecking readmark:', error);
  }
};
