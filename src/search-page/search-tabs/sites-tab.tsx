import { useAppResourcesContext } from '../app-resources-context';
import { SiteCard } from './site-card';

export const SitesTab = () => {
  const { loadingMarkup, resources } = useAppResourcesContext();

  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 p-2 max-w-7xl mx-auto'>
      {resources.map((resource, index) => (
        <SiteCard
          key={index}
          {...{
            resource,
          }}
        />
      ))}
      {loadingMarkup}
    </div>
  );
};
