import { VideoCard } from './video-card';
import { useAppResourcesContext } from '../app-resources-context';

export const VideoTab = () => {
  const { loadingMarkup, resources } = useAppResourcesContext();

  return (
    <div className='grid grid-cols-1 sm:grid-cols-2 gap-2 p-2 max-w-7xl mx-auto'>
      {resources.map((resource, index) => (
        <VideoCard
          key={index}
          {...{
            resource,
          }}
        />
      ))}
      {loadingMarkup}
    </div>
  );
};
