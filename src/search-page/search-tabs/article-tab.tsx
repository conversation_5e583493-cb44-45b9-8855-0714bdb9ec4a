import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useAppResourcesContext } from '../app-resources-context';
import { ArticleCard } from './article-card';

dayjs.extend(relativeTime);

export function ArticleTab() {
  const { loadingMarkup, resources } = useAppResourcesContext();

  return (
    <div
      className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 p-2 max-w-7xl mx-auto'
      style={{ height: 'calc(100vh - 117px)', overflowY: 'scroll' }}
    >
      {resources.map((resource, i) => {
        return (
          <ArticleCard
            key={i}
            {...{
              resource,
            }}
          />
        );
      })}
      {loadingMarkup}
    </div>
  );
}
