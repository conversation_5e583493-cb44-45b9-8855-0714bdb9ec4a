import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { useRootContext } from '../../context/useRootContext';
import { AppResource, AppTag } from '../../core.types';
import { firebaseTimestampToFriendlyDate } from '../../services/utils-service';

import { AppButtonSecondary } from '@/components/buttons/app-button-new';
import { BookmarkAction } from '../resource-actions/bookmark-action';
import { DeleteAction } from '../resource-actions/delete-action';
import { DislikeAction } from '../resource-actions/dislike-action';
import { EditAction } from '../resource-actions/edit-action';
import { LikeAction } from '../resource-actions/like-action';
import { ReadMarkAction } from '../resource-actions/readmark-action';
import { ResourceTags } from './resource-tags';

interface SiteCardProps {
  resource: AppResource;
}

export const SiteCard = ({ resource }: SiteCardProps) => {
  const { tags } = useRootContext();

  const tagsList = resource.tags.map((tagId) =>
    tags.find((tag) => tag.id === tagId),
  ) as AppTag[];

  return (
    <Card className='flex flex-col cursor-pointer'>
      <div className='flex justify-end f-full pt-2'>
        <span className='font-semibold whitespace-pre-wrap text-black dark:text-[#d4d1d1] flex gap-2 items-center text-[12px] mr-auto ml-6'>
          {firebaseTimestampToFriendlyDate(resource?.createdAt as any)}
        </span>
        <LikeAction {...{ resource }} />
        <DislikeAction {...{ resource }} />
        <BookmarkAction appResourceId={resource.id} />
        <ReadMarkAction appResourceId={resource.id} />
      </div>

      <CardContent>
        <div className='min-w-[300px] grow'>
          <div className='flex items-start w-full justify-between gap-4'>
            <img
              src={resource.imageUrl}
              alt=''
              className='size-[150px] rounded-md object-contain bg-slate-300'
            />
            <div>
              <h1 className='text-lg'>{resource.title}</h1>
              <p className='py-2 text-sm text-justify'>
                {resource.description}
              </p>
              <AppButtonSecondary
                onClick={() => {
                  window.open(resource.url, '_blank');
                }}
              >
                Visit
              </AppButtonSecondary>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className='grow'>
        <ResourceTags tags={tagsList} className='mt-auto' />
      </CardFooter>
      <div className='flex justify-end gap-2 py-2 w-full'>
        <EditAction {...{ resource }} />
        <DeleteAction resourceId={resource.id} />
      </div>
    </Card>
  );
};
