import { AppHeader } from '@/components/app-header/app-header';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { TooltipProvider } from '@/components/ui/tooltip';
import { useRootContext } from '@/context/useRootContext';
import { AppResourceType } from '@/core.types';
import { Bookmark } from 'lucide-react';
import { CSSProperties } from 'react';
import { FaFilter } from 'react-icons/fa';
import { AppAuthorSelect } from 'src/components/AppSelect/AppAuthorSelect';
import { cn } from 'src/utils';
import { useAppResourcesContext } from './app-resources-context';
import { useSearchFiltersContext } from './search-filters-context';
import { ArticleTab } from './search-tabs/article-tab';
import { GithubTab } from './search-tabs/github-tab';
import { ProposalsTab } from './search-tabs/proposals-tab';
import { SitesTab } from './search-tabs/sites-tab';
import { TweetsTab } from './search-tabs/tweets-tab';
import { VideoTab } from './search-tabs/videos-tab';
import { SearchTags } from './search-tags';

const defaultLayout = [225, 440 + 655];

export function SearchPage() {
  const { authorId, setAuthorID, resourceType, setResourceType } =
    useSearchFiltersContext();
  const { isAuthorized } = useRootContext();
  const {
    resetAndReloadResources,
    bookmarksMode,
    showBookMarks,
    hideBookMarks,
  } = useAppResourcesContext();

  const onAuthorSelectChange = (authorId: string) => {
    setAuthorID(authorId);
    resetAndReloadResources();
  };

  const onResourceTypeChange = (resourceType: AppResourceType) => {
    setResourceType(resourceType);
    if (!bookmarksMode) {
      resetAndReloadResources();
    }
  };

  const onBookmarkSwitchChange = (value: boolean) => {
    if (value) {
      showBookMarks();
    } else {
      hideBookMarks();
    }
  };

  const isMobile = window.innerWidth <= 768;

  const TabContentStyle = isMobile
    ? {}
    : ({
        height: 'calc(100vh - 117px)',
        overflowY: 'auto',
      } as CSSProperties);

  return (
    <>
      <AppHeader />
      <TooltipProvider delayDuration={0}>
        <ResizablePanelGroup
          direction={isMobile ? 'vertical' : 'horizontal'}
          className='grow'
        >
          <ResizablePanel
            defaultSize={defaultLayout[0]}
            minSize={5}
            style={{
              overflow: isMobile ? 'visible !important' : 'hidden',
              display: isMobile ? 'block' : undefined,
              height: isMobile ? 100 : '100%',
            }}
          >
            <div className={cn('py-2 px-2 flex gap-2 items-center')}>
              <AppAuthorSelect
                value={authorId}
                onChange={onAuthorSelectChange}
              />
              {isMobile && (
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant='outline' size='sm'>
                      <FaFilter />
                    </Button>
                  </DialogTrigger>
                  <DialogContent
                    className='sm:max-w-[425px] max-h-[90vh] overflow-auto'
                    onInteractOutside={(e) => {
                      e.preventDefault();
                    }}
                  >
                    <DialogHeader>
                      <DialogTitle>Select specific tags</DialogTitle>
                    </DialogHeader>
                    <SearchTags style={{}} />
                  </DialogContent>
                </Dialog>
              )}
            </div>
            <Separator />
            {!isMobile && (
              <SearchTags
                style={{ height: 'calc(100vh - 143px)', overflowY: 'auto' }}
              />
            )}
          </ResizablePanel>
          {!isMobile && <ResizableHandle withHandle />}

          <ResizablePanel
            defaultSize={defaultLayout[1]}
            minSize={30}
            // @ts-expect-error note
            style={isMobile ? { overflowY: 'scroll !important' } : {}}
          >
            <Tabs
              defaultValue={resourceType}
              onValueChange={(x) => {
                if (x == 'proposal') return;
                onResourceTypeChange(x as AppResourceType);
              }}
              className='flex flex-col'
            >
              <div className='flex items-center px-4 py-2 flex-wrap gap-2'>
                {isAuthorized && (
                  <div className='flex items-center space-x-2'>
                    <Switch
                      id='bookmarks'
                      checked={bookmarksMode}
                      onCheckedChange={onBookmarkSwitchChange}
                    />
                    <Label htmlFor='bookmarks' className='flex gap-2'>
                      {bookmarksMode ? 'Hide' : 'Show'}
                      <Bookmark className='h-4 w-4 fill-current text-yellow-500 transform transition-transform duration-150 ease-in-out active:scale-150' />
                    </Label>
                  </div>
                )}

                <TabsList className='ml-auto flex-wrap h-full px-2'>
                  <TabsTrigger
                    value={AppResourceType.Article}
                    className='text-zinc-600 dark:text-zinc-200'
                  >
                    Articles
                  </TabsTrigger>
                  <TabsTrigger
                    value={AppResourceType.Video}
                    className='text-zinc-600 dark:text-zinc-200'
                  >
                    Videos
                  </TabsTrigger>
                  <TabsTrigger
                    value={AppResourceType.Tweet}
                    className='text-zinc-600 dark:text-zinc-200'
                  >
                    Tweets
                  </TabsTrigger>
                  <TabsTrigger
                    value={AppResourceType.Github}
                    className='text-zinc-600 dark:text-zinc-200'
                  >
                    Github
                  </TabsTrigger>
                  <TabsTrigger
                    value={AppResourceType.Papers}
                    className='text-zinc-600 dark:text-zinc-200'
                  >
                    Papers
                  </TabsTrigger>
                  <TabsTrigger
                    value={AppResourceType.Sites}
                    className='text-zinc-600 dark:text-zinc-200'
                  >
                    Sites
                  </TabsTrigger>
                  {isAuthorized && (
                    <TabsTrigger
                      value='proposal'
                      className='text-zinc-600 dark:text-zinc-200'
                    >
                      My Proposals
                    </TabsTrigger>
                  )}
                </TabsList>
              </div>
              <Separator />
              <TabsContent
                value={AppResourceType.Article}
                style={TabContentStyle}
              >
                <ArticleTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Papers}
                style={TabContentStyle}
              >
                <ArticleTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Tweet}
                style={TabContentStyle}
              >
                <TweetsTab />
              </TabsContent>

              <TabsContent
                value={AppResourceType.Github}
                style={TabContentStyle}
              >
                <GithubTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Sites}
                style={TabContentStyle}
              >
                <SitesTab />
              </TabsContent>
              <TabsContent
                value={AppResourceType.Video}
                style={TabContentStyle}
              >
                <VideoTab />
              </TabsContent>
              {isAuthorized && (
                <TabsContent value='proposal' style={TabContentStyle}>
                  <ProposalsTab />
                </TabsContent>
              )}
            </Tabs>
          </ResizablePanel>
        </ResizablePanelGroup>
      </TooltipProvider>
    </>
  );
}
