import { getRatingRecord, rateAppResource } from '@/api';
import { Button } from '@/components/ui/button';
import { AppResource, RateType } from '@/core.types';
import { Heart } from 'lucide-react';
import { useCallback, useState } from 'react';
import { toast } from 'react-toastify';
import { useAppResourcesContext } from '../app-resources-context';

export const LikeAction = ({ resource }: { resource: AppResource }) => {
  const [loading, setLoading] = useState(false);
  const { onRateResource, resetLikesDislikes } = useAppResourcesContext();

  const onLikeClick = useCallback(async () => {
    if (loading) return;
    const ratingRecord = await getRatingRecord(resource.id);

    const initialLikes = resource.likesCount;
    const initialDislikes = resource.dislikesCount;

    try {
      setLoading(true);
      if (ratingRecord?.type === RateType.Like) {
        toast.error('You already liked this resource');
        setLoading(false);
        return;
      }
      onRateResource(
        resource.id,
        RateType.Like,
        ratingRecord?.type === RateType.Dislike,
      );
      await rateAppResource({
        resourceId: resource.id,
        type: RateType.Like,
      });
      setLoading(false);
    } catch (error) {
      console.log(error);
      resetLikesDislikes(resource.id, initialLikes, initialDislikes);
    }
  }, [
    loading,
    onRateResource,
    resetLikesDislikes,
    resource.dislikesCount,
    resource.id,
    resource.likesCount,
  ]);

  return (
    <div className='flex items-center'>
      <Button
        variant='ghost'
        size='icon'
        onClick={onLikeClick}
        style={{
          cursor: 'pointer',
        }}
      >
        <Heart className='h-4 w-4 text-red-500 transform transition-transform duration-150 ease-in-out active:scale-150' />
      </Button>
      {resource.likesCount > 0 && (
        <span className='ml-2'>{resource.likesCount}</span>
      )}
    </div>
  );
};
