import c from 'classnames';
import { v4 as uuidv4 } from 'uuid';
import { Tooltip } from 'react-tooltip';
import noop from 'lodash/noop';

type AppActionProps = {
  onClick?: any;
  disabled?: any;
  children: any;
  className?: string;
  width?: number;
  tooltip?: string;
};

export const AppAction = ({
  onClick = noop,
  children,
  className,
  disabled,
  tooltip,
  width = 50,
}: AppActionProps) => {
  const combinedClassName = c(
    `flex justify-start items-center gap-2 rounded-[10px] text-[25px] duration-150 w-[${width}px] h-[40px] hover:border-black`,
    className,
  );

  const tooltipID = uuidv4();

  return (
    <>
      {tooltip && <Tooltip id={tooltipID} />}
      <button
        data-tooltip-id={tooltipID}
        data-tooltip-content={tooltip}
        className={combinedClassName}
        onClick={onClick}
        disabled={disabled}
      >
        {children}
      </button>
    </>
  );
};
