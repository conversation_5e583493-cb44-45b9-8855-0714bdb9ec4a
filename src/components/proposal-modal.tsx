import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog';
import { createProposal, updateProposal } from '../api';

import { AppProposal } from '@/core.types';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { ProposalForm } from './ProposalForm';
import noop from 'lodash/noop';

export const ProposalModal = ({
  mode = 'create',
  defaultValues,
  trigger,
  onSubmitCompleted = noop,
}: {
  mode?: 'create' | 'edit';
  trigger: React.ReactNode;
  defaultValues?: AppProposal;
  onSubmitCompleted?(data: AppProposal): void;
}) => {
  const [open, setOpen] = useState(false);

  const onSubmit = async (data) => {
    if (!data.authorId && !data.customTwitterId) {
      toast.error('Please select author or write custom @twitterId');
      return;
    }
    if (!data.tags) {
      toast.error('Please select tags');
      return;
    }

    const manageProposal = mode === 'edit' ? updateProposal : createProposal;

    await manageProposal(data);
    onSubmitCompleted(data);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent
        className='sm:max-w-[425px] max-h-[90vh] overflow-auto'
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Share' : 'Edit'} your resource
          </DialogTitle>
        </DialogHeader>
        <ProposalForm {...{ onSubmit, defaultValues }} />
      </DialogContent>
    </Dialog>
  );
};
